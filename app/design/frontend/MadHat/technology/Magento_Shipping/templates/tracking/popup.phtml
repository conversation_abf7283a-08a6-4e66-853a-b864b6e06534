<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Helper\SecureHtmlRenderer;
use Magento\Shipping\Block\Tracking\Popup;

/** @var Escaper $escaper */
/** @var Popup $block */
/** @var SecureHtmlRenderer $secureRenderer */

$results = $block->getTrackingInfo();
?>
<div class="page tracking container">
    <?php if (!empty($results)): ?>
        <?php foreach ($results as $shipId => $result): ?>
            <?php if ($shipId): ?>
                <div class="order subtitle caption">
                    <?= /* @noEscape */ $escaper->escapeHtml(__('Shipment #')) . $shipId ?>
                </div>
            <?php endif; ?>
            <?php if (!empty($result)): ?>
                <?php foreach ($result as $counter => $track): ?>
                    <div class="table-wrapper">
                        <?php
                            $shipmentBlockIdentifier = $shipId . '.' . $counter;
                            // Use the custom block class instead of generic Template class
                            $block->addChild('shipping.tracking.details.' . $shipmentBlockIdentifier, \MadHat\SiteIntegrationShipping\Block\Tracking\Popup::class, [
                                'track' => $track,
                                'template' => 'Magento_Shipping::tracking/details.phtml',
                                'storeSupportEmail' => $block->getStoreSupportEmail()
                            ]);
                        ?>
                        <?= /* @noEscape */ $block->getChildHtml('shipping.tracking.details.' .
                            $shipmentBlockIdentifier) ?>
                    </div>
                    <?php if (is_object($track) && !empty($track->getProgressdetail())): ?>
                        <?php
                            $block->addChild(
                                'shipping.tracking.progress.' . $shipmentBlockIdentifier,
                                Template::class,
                                ['track' => $track, 'template' => 'Magento_Shipping::tracking/progress.phtml']
                            );
                        ?>
                        <?= /* @noEscape */ $block->getChildHtml('shipping.tracking.progress.' .
                            $shipmentBlockIdentifier) ?>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="message info empty">
                    <div><?= $escaper->escapeHtml(__('There is no tracking available for this shipment.')) ?></div>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="message info empty">
            <div><?= $escaper->escapeHtml(__('There is no tracking available.')) ?></div>
        </div>
    <?php endif; ?>
</div>
