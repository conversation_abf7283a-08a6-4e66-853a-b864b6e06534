<?php

namespace MadHat\SiteIntegrationShipping\Block\Tracking;

class Popup extends \Magento\Shipping\Block\Tracking\Popup
{
    public function getTest()
    {
        return 'test1';
    }

    /**
     * Get description for current tracking
     *
     * This method retrieves the description from the sales_shipment_track.description column
     * which typically contains tracking URLs or additional tracking information
     *
     * @return string|null
     */
    public function getTrackingDescription()
    {
        $track = $this->getData('track');

        // Debug: Let's see what we're working with
        $debugInfo = [];
        $debugInfo['track_type'] = is_object($track) ? get_class($track) : gettype($track);

        if (is_object($track)) {
            $debugInfo['methods'] = get_class_methods($track);
            $debugInfo['has_getDescription'] = method_exists($track, 'getDescription');

            // Try different possible method names
            if (method_exists($track, 'getDescription')) {
                $debugInfo['description_value'] = $track->getDescription();
            }
            if (method_exists($track, 'getData')) {
                $debugInfo['all_data'] = $track->getData();
                $debugInfo['description_from_getData'] = $track->getData('description');
            }
        } elseif (is_array($track)) {
            $debugInfo['array_keys'] = array_keys($track);
            $debugInfo['description_from_array'] = isset($track['description']) ? $track['description'] : 'not_set';
        }

        // Store debug info for template access
        $this->setData('debug_info', $debugInfo);

        // Handle track object (most common case)
        if (is_object($track)) {
            // Try getDescription() method first
            if (method_exists($track, 'getDescription')) {
                $description = $track->getDescription();
                if (!empty($description)) {
                    return $description;
                }
            }

            // Try getData('description') method
            if (method_exists($track, 'getData')) {
                $description = $track->getData('description');
                if (!empty($description)) {
                    return $description;
                }
            }
        }

        // Handle track array (fallback case)
        if (is_array($track) && isset($track['description'])) {
            $description = $track['description'];
            return !empty($description) ? $description : null;
        }

        return null;
    }

    /**
     * Get tracking URL from description if it's a valid URL
     *
     * @return string|null
     */
    public function getTrackingUrl()
    {
        $description = $this->getTrackingDescription();

        if ($description && filter_var($description, FILTER_VALIDATE_URL)) {
            return $description;
        }

        return null;
    }

}
